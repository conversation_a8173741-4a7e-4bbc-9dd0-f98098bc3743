async function weeklyKrProgress({ dv }) {
	// 获取当前系统时间的ISO周数和年份
	function getISOWeek(date) {
		const d = new Date(date);
		d.setHours(0, 0, 0, 0);
		d.setDate(d.getDate() + 4 - (d.getDay() || 7));
		const yearStart = new Date(d.getFullYear(), 0, 1);
		const weekNo = Math.ceil(((d - yearStart) / 86400000 + 1) / 7);
		return [d.getFullYear(), weekNo];
	}

	const [targetYear, targetWeek] = getISOWeek(new Date());
	const projectName = dv.current().file.path.split("/")[1].trim();
	const targetFolder = `2-项目/${projectName}/3-每周评审`;
	const tableTitlePattern = "KR进度";

	let allTableData = [];

	// 第一步：快速筛选符合条件的文件
	const candidateFiles = [];
	for (let file of dv.pages(`"${targetFolder}"`).file) {
		const fileMatch = file.name.match(/^Review-(\d{4})-WK(\d{2})$/);
		if (!fileMatch) continue;
		
		const fileYear = parseInt(fileMatch[1]);
		const fileWeek = parseInt(fileMatch[2]);

		if (fileYear < targetYear || (fileYear === targetYear && fileWeek < targetWeek)) {
			candidateFiles.push({
				file,
				year: fileYear,
				week: fileWeek,
				path: file.path
			});
		}
	}

	// 第二步：按时间倒序排序（从最新到最早）
	candidateFiles.sort((a, b) => {
		if (a.year !== b.year) return b.year - a.year;
		return b.week - a.week;
	});

	// 第三步：仅处理最近4周的文件，找到第一个有效文件即停止
	let foundFile = null;
	let foundTables = [];
	
	for (let i = 0; i < Math.min(4, candidateFiles.length); i++) {
		const candidate = candidateFiles[i];
		const { path } = candidate;
		
		// 加载文件内容
		const content = await dv.io.load(path);
		
		// 识别指定标题下的内容区域
		const headingRegex = new RegExp(
			`(?:^|\\n)#+\\s*.*${tableTitlePattern}.*[^\\n]*\\n([\\s\\S]*?)(?=\\n#|$)`,
			"i"
		);
		
		const match = content.match(headingRegex);
		if (!match || !match[1]) continue;
		
		const sectionContent = match[1].trim();
		console.log("找到KR进度部分内容:");
		console.log(sectionContent);
		
		// 表格解析函数 - 增强版，确保表格结构完整性
		const parseMarkdownTables = (markdown) => {
			const tables = [];
			// 更宽松的正则表达式来匹配完整的表格
			const tableRegex = /\|(.+)\|\s*\n\s*\|(.+)\|\s*\n((?:\|.+\|\s*\n?)*)/g;
			
			let tableMatch;
			while ((tableMatch = tableRegex.exec(markdown)) !== null) {
				try {
					console.log("=== 表格匹配详情 ===");
					console.log("完整匹配:", tableMatch[0]);
					console.log("表头部分:", tableMatch[1]);
					console.log("分隔符部分:", tableMatch[2]);
					console.log("数据部分:", tableMatch[3]);
					console.log("匹配组数量:", tableMatch.length);
					
					// 更智能的表头解析 - 处理包含复杂链接语法的单元格
					const headerLine = "|" + tableMatch[1] + "|";
					const headerRow = [];
					
					// 使用状态机方法解析表头，处理嵌套的 [[ ]] 和 $= 语法
					let currentCell = '';
					let bracketDepth = 0;
					let inEscape = false;
					
					for (let i = 0; i < headerLine.length; i++) {
						const char = headerLine[i];
						
						if (inEscape) {
							currentCell += char;
							inEscape = false;
							continue;
						}
						
						if (char === '\\') {
							inEscape = true;
							continue;
						}
						
						if (char === '[') {
							bracketDepth++;
							currentCell += char;
						} else if (char === ']') {
							bracketDepth--;
							currentCell += char;
						} else if (char === '|' && bracketDepth === 0) {
							// 只有在括号平衡时才认为是单元格分隔符
							if (currentCell.trim()) {
								headerRow.push(currentCell.trim());
							}
							currentCell = '';
						} else {
							currentCell += char;
						}
					}
					
					// 添加最后一个单元格
					if (currentCell.trim()) {
						headerRow.push(currentCell.trim());
					}
					
					// 验证表头有效性 - 必须包含有效列
					if (headerRow.length === 0) {
						continue;
					}
					
					// 处理数据行 - 增强验证
					const dataRows = tableMatch[3].split('\n')
						.filter(row => {
							const trimmed = row.trim();
							return trimmed !== '' &&
								   trimmed.includes('|') &&
								   !trimmed.startsWith('|--');
						})
						.map(row => {
							console.log("原始数据行:", row);
							
							// 更简单的方法：直接按|分割，然后处理特殊情况
							const parts = row.split('|');
							console.log("按|分割的结果:", parts);
							
							// 移除首尾的空元素（Markdown表格的边界）
							const cells = parts.slice(1, -1);
							console.log("移除边界后的单元格:", cells);
							
							// 标准化每个单元格
							const normalizedCells = cells.map(cell => {
								return cell.trim()
									.replace(/<br>/gi, '\n')
									.replace(/\s*\n\s*/g, '\n')
									.trim();
							});
							
							console.log("标准化后的单元格:", normalizedCells);
							console.log("标准化后列数:", normalizedCells.length, "期望列数:", headerRow.length);
							
							// 确保数据行与表头列数完全匹配
							while (normalizedCells.length < headerRow.length) {
								console.log(`补齐第${normalizedCells.length + 1}列`);
								normalizedCells.push(''); // 补齐缺失的列
							}
							
							// 如果数据行过多，截断多余的列
							if (normalizedCells.length > headerRow.length) {
								console.log(`截断多余的列，从${normalizedCells.length}到${headerRow.length}`);
								normalizedCells.splice(headerRow.length);
							}
							
							// 输出每列内容以便调试
							normalizedCells.forEach((cell, index) => {
								console.log(`第${index + 1}列内容: "${cell}"`);
							});
							
							return normalizedCells;
						})
						.filter(row => {
							// 更严格的过滤：行内至少有一个非空单元格
							return row.some(cell => cell !== '' && cell !== '-' && cell !== '~');
						});
					
					// 增强验证：必须同时满足数据行存在且结构有效
					if (dataRows.length > 0 && headerRow.length > 0) {
						// 调试输出
						console.log("表头解析结果:", headerRow);
						console.log("数据行样例:", dataRows[0]);
						console.log("表头列数:", headerRow.length, "数据行列数:", dataRows[0].length);
						
						// 放宽列数匹配限制，确保不丢失有效数据
						const validDataRows = dataRows.filter(row =>
							row.length > 0 &&
							Math.abs(row.length - headerRow.length) <= 3  // 放宽到允许3列差异
						);
						
						// 如果数据行列数不足，用空字符串补齐
						const normalizedDataRows = validDataRows.map(row => {
							if (row.length < headerRow.length) {
								// 补齐缺失的列
								while (row.length < headerRow.length) {
									row.push('');
								}
							} else if (row.length > headerRow.length) {
								// 如果数据行列数过多，截断或合并最后几列
								if (row.length - headerRow.length <= 2) {
									// 少量多余列，合并到最后一个有效列
									const extraData = row.slice(headerRow.length - 1).join(' ');
									row = row.slice(0, headerRow.length - 1);
									row[headerRow.length - 2] += ' ' + extraData;
								} else {
									// 过多多余列，直接截断
									row = row.slice(0, headerRow.length);
								}
							}
							return row;
						});
						
						if (normalizedDataRows.length > 0) {
							tables.push({ header: headerRow, data: normalizedDataRows });
						}
					}
				} catch (e) {
					console.warn("表格解析错误:", e);
				}
			}
			return tables;
		};
		
		// 解析表格
		const tables = parseMarkdownTables(sectionContent);
		
		// 找到有效文件，记录并跳出循环
		if (tables.length > 0) {
			foundFile = candidate;
			foundTables = tables;
			break;
		}
	}
	
	// 处理找到的文件
	if (foundFile && foundTables.length > 0) {
		// 从 foundFile 中获取 year 和 week
		const { year, week } = foundFile;
		// 创建文件链接
		const weekTag = `${year}-WK${week.toString().padStart(2, '0')}`;
		const fileName = `Review-${weekTag}.md`;
		const fileLink = dv.fileLink(`${targetFolder}/${fileName}`, false, fileName);
		
		// 处理每个表格
		for (const table of foundTables) {
			const { header, data } = table;
			
			if (allTableData.length === 0) {
				allTableData.push([...header, "回顾链接"]);
			}
			
			data.forEach(row => {
				allTableData.push([...row, fileLink]);
			});
		}
	}

	// 按Blocker ID排序
	if (allTableData.length > 1) {
		const headers = allTableData[0];
		const dataRows = allTableData.slice(1);
		
		const blockerIdColumnIndex = headers.indexOf("来源");
		
		dataRows.sort((a, b) => {
			const blockerIdA = a[blockerIdColumnIndex] || "";
			const blockerIdB = b[blockerIdColumnIndex] || "";
			
			const dateMatchA = blockerIdA.match(/blocker-(\d{8})/);
			const dateMatchB = blockerIdB.match(/blocker-(\d{8})/);
			
			if (dateMatchA && dateMatchB) {
				return dateMatchB[1].localeCompare(dateMatchA[1]);
			}
			else if (dateMatchA) {
				return -1;
			}
			else if (dateMatchB) {
				return 1;
			}
			return blockerIdA.localeCompare(blockerIdB);
		});
		
		allTableData = [headers, ...dataRows];
	}

	// 输出结果
	if (allTableData.length > 0) {
		dv.table(allTableData[0], allTableData.slice(1));
	} else {
		dv.el("p", "📊 未找到近期的KR进度数据");
	}
}

weeklyKrProgress(input);
