---
createdDate: 2025-09-12
aliases:
  - dataviewjs查询导致页面闪烁
location:
  - 项目首页
  - 每周计划
  - 每周评审
  - 每周回顾
type: 系统债
priority: P2
status: 进行中
relatedDebt:
cssclasses:
  - c4
---
# 1. 问题诊断

| 序号  | 类型   | 场景描述                                                                               | 可观测现象        | 关键影响            | 触发条件（如何发现？） | 根因分析    |
| --- | ---- | ---------------------------------------------------------------------------------- | ------------ | --------------- | ----------- | ------- |
| 1   | 原始债务 | 在 Obsidian 中使用 DataviewJS 查询，并开启视图自动刷新功能。在编辑笔记过程中，图表以默认间隔（2500ms）频繁重新渲染，导致页面视觉不稳定。 | 页面周期性闪烁，内容跳动 | 干扰编辑专注度，降低阅读流畅性 | 编辑包含图表的笔记时  | 插件原始BUG |

# 2. 临时方案
| 序号  | 生效时间       | 行动                                            | 退出条件       |
| --- | ---------- | --------------------------------------------- | ---------- |
| 1   | 2025-09-12 | 关闭dataview插件自动刷新功能（Automatic view refreshing） | 插件BUG优化上线后 |
# 4. 偿还计划

- [ ] 

# 5. 验收清单

- `性能指标`：
- `质量要求`：
- `测试覆盖`：
- `文档更新`：
- `监控告警`：